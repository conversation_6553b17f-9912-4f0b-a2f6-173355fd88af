// SPDX-License-Identifier: MIT
pragma solidity ^0.8.0;

contract TestMemoryBehavior {
    
    struct TestData {
        uint256 value;
    }
    
    struct TestList {
        TestData[] items;
        uint256 length;
    }
    
    function testMemoryModification() external pure returns (uint256, uint256) {
        // Создаем структуру в memory
        TestList memory list = TestList({
            items: new TestData[](10),
            length: 0
        });
        
        uint256 lengthBefore = list.length;
        
        // Передаем в функцию
        modifyList(list);
        
        uint256 lengthAfter = list.length;
        
        return (lengthBefore, lengthAfter);
    }
    
    function modifyList(TestList memory _list) internal pure {
        // Добавляем элемент и увеличиваем length
        _list.items[_list.length] = TestData({value: 100});
        _list.length++;
    }
    
    function testMemoryModificationWithReturn() external pure returns (uint256, uint256) {
        // Создаем структуру в memory
        TestList memory list = TestList({
            items: new TestData[](10),
            length: 0
        });
        
        uint256 lengthBefore = list.length;
        
        // Передаем в функцию и получаем результат
        list = modifyListWithReturn(list);
        
        uint256 lengthAfter = list.length;
        
        return (lengthBefore, lengthAfter);
    }
    
    function modifyListWithReturn(TestList memory _list) internal pure returns (TestList memory) {
        // Добавляем элемент и увеличиваем length
        _list.items[_list.length] = TestData({value: 100});
        _list.length++;
        return _list;
    }
}
